"""
Example usage of the reCAPTCHA solver.
"""
from recaptcha_solver import solver
import os
import time

def main():
    # Default configuration
    url = "https://www.google.com/recaptcha/api2/demo"
    headless = False
    verbose = True
    proxy = None

    print("Starting reCAPTCHA Solver Example...\n")

    # Set environment variable to ignore certificate errors
    os.environ['PYTHONHTTPSVERIFY'] = '0'

    print(f"\nAttempting to solve reCAPTCHA on: {url}\n")
    print("Note: If you're running this locally and seeing certificate errors,")
    print("the script has been modified to attempt to bypass these errors.\n")

    start_time = time.time()

    result = solver(
        url=url,
        proxy=proxy,        # No proxy by default
        cookies=None,       # No initial cookies
        headless=headless,  # Non-headless mode by default
        verbose=verbose     # Verbose output by default
    )

    end_time = time.time()
    total_time = end_time - start_time

    print("\n--- reCAPTCHA Solving Result ---")
    if result and result.get('success') and result.get('recaptcha_token'):
        print("Success! reCAPTCHA appears to be solved.")
        token = result['recaptcha_token']
        print(f"Token: {token}")
        print(f"Time taken: {result['time_taken']} seconds")
    elif result and not result.get('recaptcha_token'):
        print("Process completed, but reCAPTCHA token was NOT found.")
        print(f"Time taken: {result['time_taken']} seconds")
    else:
        print("Failed to solve reCAPTCHA or an error occurred.")
        if result and 'error' in result:
            print(f"Error: {result['error']}")
        print(f"Total time: {total_time:.2f} seconds")

    print("\nExample finished.")

if __name__ == "__main__":
    main()
