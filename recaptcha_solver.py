"""
reCAPTCHA solver implementation.
This module provides a complete solution for solving reCAPTCHA challenges.
"""
# Standard imports
import os
import re
import shutil
import urllib3
from time import sleep, time

# Third-party imports
import cv2
import numpy as np
import requests
from PIL import Image
from ultralytics import YOLO
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriver<PERSON><PERSON>
from seleniumwire import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# Constants
GRID_SIZE_3X3 = 100
GRID_SIZE_4X4 = 112.5
IFRAME_TIMEOUT = 60
ELEMENT_TIMEOUT = 10
DEFAULT_DELAY_MU = 0.3
DEFAULT_DELAY_SIGMA = 0.1
VERIFY_DELAY_MU = 2.0
VERIFY_DELAY_SIGMA = 0.2

# Target mappings for object detection
TARGET_MAPPINGS = {
    "bicycle": 1,
    "bus": 5,
    "boat": 8,
    "car": 2,
    "hydrant": 10,
    "motorcycle": 3,
    "traffic": 9
}

def find_between(s, first, last):
    """
    Find a substring between two substrings.
    :param s: string to search.
    :param first: first substring.
    :param last: last substring.
    """
    try:
        start = s.index(first) + len(first)
        end = s.index(last, start)
        return s[start:end]
    except ValueError:
        return ""

def random_delay(mu=DEFAULT_DELAY_MU, sigma=DEFAULT_DELAY_SIGMA):
    """
    Random delay to simulate human behavior.
    :param mu: mean of normal distribution.
    :param sigma: standard deviation of normal distribution.
    """
    delay = max(0.1, np.random.normal(mu, sigma))
    sleep(delay)

def go_to_recaptcha_iframe1(driver):
    """
    Go to the first recaptcha iframe. (CheckBox)
    """
    driver.switch_to.default_content()
    recaptcha_iframe1 = WebDriverWait(driver=driver, timeout=IFRAME_TIMEOUT).until(
        EC.presence_of_element_located((By.XPATH, '//iframe[@title="reCAPTCHA"]')))
    driver.switch_to.frame(recaptcha_iframe1)

def go_to_recaptcha_iframe2(driver):
    """
    Go to the second recaptcha iframe. (Images)
    """
    driver.switch_to.default_content()
    recaptcha_iframe2 = WebDriverWait(driver=driver, timeout=IFRAME_TIMEOUT).until(
        EC.presence_of_element_located((By.XPATH, '//iframe[contains(@title, "challenge")]')))
    driver.switch_to.frame(recaptcha_iframe2)

def get_target_num(driver):
    """
    Get the target number from the recaptcha title.
    """
    target = WebDriverWait(driver, ELEMENT_TIMEOUT).until(EC.presence_of_element_located(
        (By.XPATH, '//div[@id="rc-imageselect"]//strong')))

    target_text = target.text.lower()
    for term, value in TARGET_MAPPINGS.items():
        if term in target_text:
            return value

    return 1000

def dynamic_and_selection_solver(target_num, verbose, model):
    """
    Get the answers from the recaptcha images.
    :param target_num: target number.
    :param verbose: print verbose.
    """
    try:
        # Load image and predict
        with Image.open("0.png") as image:
            image_array = np.asarray(image)
            result = model.predict(image_array, task="detect", verbose=verbose)

        if not result or not result[0].boxes:
            return []

        # Get the index of the target number
        target_indices = [i for i, cls in enumerate(result[0].boxes.cls) if cls == target_num]

        if not target_indices:
            return []

        # Get the answers from the index
        answers = []
        boxes = result[0].boxes.data

        for i in target_indices:
            target_box = boxes[i]
            x1, y1, x2, y2 = int(target_box[0]), int(target_box[1]), int(target_box[2]), int(target_box[3])

            # Calculate center coordinates
            xc, yc = (x1 + x2) / 2, (y1 + y2) / 2

            # Calculate grid position (3x3 grid)
            row = int(yc // GRID_SIZE_3X3)
            col = int(xc // GRID_SIZE_3X3)
            answer = row * 3 + col + 1

            if 1 <= answer <= 9:  # Validate answer is within grid
                answers.append(answer)

        return list(set(answers))
    except Exception as e:
        if verbose:
            print(f"Error in dynamic_and_selection_solver: {e}")
        return []

def get_all_captcha_img_urls(driver):
    """
    Get all the image urls from the recaptcha.
    """
    images = WebDriverWait(driver, ELEMENT_TIMEOUT).until(EC.presence_of_all_elements_located(
        (By.XPATH, '//div[@id="rc-imageselect-target"]//img')))

    return [img.get_attribute("src") for img in images]

def download_img(name, url):
    """
    Download the image.
    :param name: name of the image.
    :param url: url of the image.
    """
    try:
        response = requests.get(url, stream=True, timeout=10)
        response.raise_for_status()
        with open(f'{name}.png', 'wb') as out_file:
            shutil.copyfileobj(response.raw, out_file)
    except requests.RequestException as e:
        print(f"Error downloading image {name}: {e}")
        raise
    finally:
        if 'response' in locals():
            response.close()

def get_all_new_dynamic_captcha_img_urls(answers, before_img_urls, driver):
    """
    Get all the new image urls from the recaptcha.
    :param answers: answers from the recaptcha.
    :param before_img_urls: image urls before.
    """
    try:
        images = WebDriverWait(driver, ELEMENT_TIMEOUT).until(EC.presence_of_all_elements_located(
            (By.XPATH, '//div[@id="rc-imageselect-target"]//img')))

        img_urls = [img.get_attribute("src") for img in images]

        # Check if any of the answered images have changed
        for answer in answers:
            if answer <= len(img_urls) and answer <= len(before_img_urls):
                if img_urls[answer-1] == before_img_urls[answer-1]:
                    return False, img_urls

        return True, img_urls
    except Exception:
        return False, []

def paste_new_img_on_main_img(main, new, loc):
    """
    Paste the new image on the main image.
    :param main: main image.
    :param new: new image.
    :param loc: location of the new image.
    """
    try:
        paste = np.copy(main)

        row = (loc - 1) // 3
        col = (loc - 1) % 3

        start_row, end_row = row * GRID_SIZE_3X3, (row + 1) * GRID_SIZE_3X3
        start_col, end_col = col * GRID_SIZE_3X3, (col + 1) * GRID_SIZE_3X3

        paste[start_row:end_row, start_col:end_col] = new

        paste_bgr = cv2.cvtColor(paste, cv2.COLOR_RGB2BGR)
        cv2.imwrite('0.png', paste_bgr)
    except Exception as e:
        print(f"Error pasting image: {e}")
        raise

def get_occupied_cells(vertices):
    """
    Get the occupied cells from the vertices.
    :param vertices: vertices of the image.
    """
    occupied_cells = set()
    rows, cols = zip(*[((v-1)//4, (v-1) % 4) for v in vertices])

    for i in range(min(rows), max(rows)+1):
        for j in range(min(cols), max(cols)+1):
            occupied_cells.add(4*i + j + 1)

    return sorted(list(occupied_cells))

def _get_grid_cell_4x4(x, y):
    """Helper function to determine which cell a coordinate belongs to in 4x4 grid."""
    grid_boundaries = [GRID_SIZE_4X4, GRID_SIZE_4X4 * 2, GRID_SIZE_4X4 * 3, GRID_SIZE_4X4 * 4]

    col = next((i + 1 for i, boundary in enumerate(grid_boundaries) if x <= boundary), 4)
    row = next((i + 1 for i, boundary in enumerate(grid_boundaries) if y <= boundary), 4)

    return (row - 1) * 4 + col

def square_solver(target_num, verbose, model):
    """
    Get the answers from the recaptcha images.
    :param target_num: target number.
    :param verbose: print verbose.
    """
    try:
        # Load image and predict
        with Image.open("0.png") as image:
            image_array = np.asarray(image)
            result = model.predict(image_array, task="detect", verbose=verbose)

        if not result or not result[0].boxes:
            return []

        boxes = result[0].boxes.data
        target_indices = [i for i, cls in enumerate(result[0].boxes.cls) if cls == target_num]

        if not target_indices:
            return []

        answers = []
        for i in target_indices:
            target_box = boxes[i]
            x1, y1, x2, y2 = int(target_box[0]), int(target_box[1]), int(target_box[2]), int(target_box[3])

            # Get all four corners of the bounding box
            corners = [(x1, y1), (x2, y1), (x1, y2), (x2, y2)]

            four_cells = []
            for x, y in corners:
                cell = _get_grid_cell_4x4(x, y)
                if 1 <= cell <= 16:
                    four_cells.append(cell)

            if four_cells:
                occupied_cells = get_occupied_cells(four_cells)
                answers.extend(occupied_cells)

        return sorted(list(set(answers)))
    except Exception as e:
        if verbose:
            print(f"Error in square_solver: {e}")
        return []

def solve_recaptcha(driver, verbose):
    """
    Solve the recaptcha.
    :param driver: selenium driver.
    :param verbose: print verbose.
    """
    try:
        go_to_recaptcha_iframe1(driver)

        WebDriverWait(driver, ELEMENT_TIMEOUT).until(EC.element_to_be_clickable(
            (By.XPATH, '//div[@class="recaptcha-checkbox-border"]'))).click()

        go_to_recaptcha_iframe2(driver)

        model = YOLO("./models/models.onnx", task="detect")

    while True:
        try:
            while True:
                reload = WebDriverWait(driver, ELEMENT_TIMEOUT).until(
                    EC.element_to_be_clickable((By.ID, 'recaptcha-reload-button')))
                title_wrapper = WebDriverWait(driver, ELEMENT_TIMEOUT).until(
                    EC.presence_of_element_located((By.ID, 'rc-imageselect')))

                target_num = get_target_num(driver)

                if target_num == 1000:
                    random_delay()
                    if verbose:
                        print("Skipping unsupported target")
                    reload.click()
                    continue

                title_text = title_wrapper.text.lower()
                img_urls = get_all_captcha_img_urls(driver)
                download_img(0, img_urls[0])

                if "squares" in title_text:
                    if verbose:
                        print("Square captcha found...")
                    answers = square_solver(target_num, verbose, model)
                    if 1 <= len(answers) < 16:
                        captcha = "squares"
                        break
                elif "none" in title_text:
                    if verbose:
                        print("Found a 3x3 dynamic captcha")
                    answers = dynamic_and_selection_solver(target_num, verbose, model)
                    if len(answers) >= 1:
                        captcha = "dynamic"
                        break
                else:
                    if verbose:
                        print("Found a 3x3 one time selection captcha")
                    answers = dynamic_and_selection_solver(target_num, verbose, model)
                    if len(answers) >= 1:
                        captcha = "selection"
                        break

                # If we reach here, reload and try again
                reload.click()
                WebDriverWait(driver, ELEMENT_TIMEOUT).until(EC.element_to_be_clickable(
                    (By.XPATH, '(//div[@id="rc-imageselect-target"]//td)[1]')))

            if captcha == "dynamic":
                for answer in answers:
                    WebDriverWait(driver, 10).until(EC.element_to_be_clickable(
                        (By.XPATH, f'(//div[@id="rc-imageselect-target"]//td)[{answer}]'))).click()
                    random_delay(mu=0.5, sigma=0.2)
                while True:
                    before_img_urls = img_urls
                    while True:
                        is_new, img_urls = get_all_new_dynamic_captcha_img_urls(
                            answers, before_img_urls, driver)
                        if is_new:
                            break

                    new_img_index_urls = []
                    for answer in answers:
                        new_img_index_urls.append(answer-1)
                    new_img_index_urls

                    for index in new_img_index_urls: download_img(index+1, img_urls[index])
                    while True:
                        try:
                            for answer in answers:
                                main_img = Image.open("0.png")
                                new_img = Image.open(f"{answer}.png")
                                location = answer
                                paste_new_img_on_main_img(
                                    main_img, new_img, location)
                            break
                        except:
                            while True:
                                is_new, img_urls = get_all_new_dynamic_captcha_img_urls(
                                    answers, before_img_urls, driver)
                                if is_new:
                                    break
                            new_img_index_urls = []
                            for answer in answers:
                                new_img_index_urls.append(answer-1)

                            for index in new_img_index_urls:
                                download_img(index+1, img_urls[index])

                    answers = dynamic_and_selection_solver(target_num, verbose, model)

                    if len(answers) >= 1:
                        for answer in answers:
                            WebDriverWait(driver, 10).until(EC.element_to_be_clickable(
                                (By.XPATH, f'(//div[@id="rc-imageselect-target"]//td)[{answer}]'))).click()
                            random_delay(mu=0.5, sigma=0.1)
                    else:
                        break
            elif captcha == "selection" or captcha == "squares":
                for answer in answers:
                    WebDriverWait(driver, 10).until(EC.element_to_be_clickable(
                        (By.XPATH, f'(//div[@id="rc-imageselect-target"]//td)[{answer}]'))).click()
                    random_delay()

            verify = WebDriverWait(driver, ELEMENT_TIMEOUT).until(EC.element_to_be_clickable(
                (By.ID, "recaptcha-verify-button")))
            random_delay(mu=VERIFY_DELAY_MU, sigma=VERIFY_DELAY_SIGMA)
            verify.click()

            try:
                go_to_recaptcha_iframe1(driver)
                WebDriverWait(driver, 4).until(
                    EC.presence_of_element_located((By.XPATH, '//span[contains(@aria-checked, "true")]')))
                if verbose:
                    print("reCAPTCHA solved successfully!")
                driver.switch_to.default_content()
                break
            except:
                if verbose:
                    print("Verification failed, retrying...")
                go_to_recaptcha_iframe2(driver)
        except Exception as e:
            if verbose:
                print(f"Error in solve_recaptcha: {e}")
            # Try to recover by going back to iframe2
            try:
                go_to_recaptcha_iframe2(driver)
            except:
                pass
    except Exception as e:
        if verbose:
            print(f"Critical error in solve_recaptcha: {e}")
        raise

def solver(url: str, cookies: dict=None, proxy: str=None, verbose=False, headless=True):
    """
    Solve the recaptcha.
    :param url: url of the recaptcha.
    :param cookies: cookies of the recaptcha.
    :param proxy: proxy for seleniumwire.
    :param verbose: print verbose.
    :param headless: run in headless mode.
    """
    # Disable SSL warnings
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # Set environment variable to ignore certificate errors
    os.environ['PYTHONHTTPSVERIFY'] = '0'

    # Set Up seleniumwire options with proxy if provided
    seleniumwire_options = {
        'verify_ssl': False,
        'disable_encoding': True,
        'suppress_connection_errors': True
    }

    if proxy:
        seleniumwire_options['proxy'] = {
            'http': f'http://{proxy}',
            'https': f'https://{proxy}',
            'no_proxy': 'localhost,127.0.0.1'
        }

    # Set Up Chrome Options with headless and no image loading if provided
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--ignore-certificate-errors')
    chrome_options.add_argument('--ignore-certificate-errors-spki-list')
    chrome_options.add_argument('--ignore-ssl-errors')
    chrome_options.add_argument('--allow-insecure-localhost')
    chrome_options.add_argument('--allow-running-insecure-content')
    chrome_options.add_argument('--disable-web-security')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--lang=en-US')

    # Add capability to accept insecure certs
    chrome_options.add_argument('--accept-insecure-certs')

    if headless:
        chrome_options.add_argument('--headless')

    driver = None
    try:
        # Initialize driver with options and cookies if provided
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(
            service=service,
            options=chrome_options,
            seleniumwire_options=seleniumwire_options
        )
        driver.scopes = ['.*google.com/recaptcha.*']

        # Set page load timeout
        driver.set_page_load_timeout(30)

        # Navigate to URL
        if verbose:
            print(f"Navigating to {url}")
        driver.get(url)

        # Add cookies if provided
        if cookies:
            for cookie in cookies:
                try:
                    driver.add_cookie(cookie)
                except Exception as e:
                    if verbose:
                        print(f"Error adding cookie: {str(e)}")

        start_time = time()
        solve_recaptcha(driver, verbose)

        # Get recaptcha token and cookies
        token = None
        try:
            for request in driver.requests:
                if 'recaptcha/api2/userverify' in request.url and request.response:
                    response_body = request.response.body.decode('utf-8')
                    token = find_between(response_body, 'uvresp","', '"')
                    if token:
                        break
        except Exception as e:
            if verbose:
                print(f"Error extracting token: {e}")

        result_cookies = driver.get_cookies()
        time_taken = round(time() - start_time, 2)

        return {
            "recaptcha_token": token,
            "cookies": result_cookies,
            "time_taken": time_taken,
            "success": token is not None
        }
    except Exception as e:
        if verbose:
            print(f"Error: {str(e)}")
        return {"success": False, "error": str(e), "time_taken": 0}
    finally:
        # Ensure driver is properly closed
        if driver:
            try:
                driver.quit()
            except Exception as e:
                if verbose:
                    print(f"Error closing driver: {e}")



